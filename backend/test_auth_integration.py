#!/usr/bin/env python3
"""
Authentication Integration Test Script
Tests the complete authentication flow from frontend to backend
"""

import asyncio
import httpx
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Test configuration
AUTO_LOGIN_SERVICE_URL = os.getenv('AUTO_LOGIN_SERVICE_URL', 'http://localhost:3000')
BACKEND_API_URL = os.getenv('BACKEND_API_URL', 'http://localhost:8000')

class AuthIntegrationTester:
    def __init__(self):
        self.auto_login_url = AUTO_LOGIN_SERVICE_URL
        self.backend_url = BACKEND_API_URL
        self.access_token = None
        self.user_info = None

    async def test_auto_login_service(self):
        """Test if auto-login service is running"""
        print("🔧 Testing auto-login service connection...")
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.auto_login_url}/health", timeout=5.0)
                
                if response.status_code == 200:
                    print("✅ Auto-login service is running")
                    return True
                else:
                    print(f"❌ Auto-login service returned status {response.status_code}")
                    return False
                    
        except httpx.RequestError as e:
            print(f"❌ Cannot connect to auto-login service: {e}")
            print(f"   Make sure the service is running at {self.auto_login_url}")
            return False

    async def test_backend_service(self):
        """Test if backend service is running"""
        print("🔧 Testing backend service connection...")
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.backend_url}/health", timeout=5.0)
                
                if response.status_code == 200:
                    print("✅ Backend service is running")
                    return True
                else:
                    print(f"❌ Backend service returned status {response.status_code}")
                    return False
                    
        except httpx.RequestError as e:
            print(f"❌ Cannot connect to backend service: {e}")
            print(f"   Make sure the service is running at {self.backend_url}")
            return False

    async def test_auth_endpoints(self):
        """Test authentication endpoints"""
        print("🔧 Testing authentication endpoints...")
        
        # Test auth routes exist
        endpoints_to_test = [
            "/api/auth/login",
            "/api/auth/me", 
            "/api/auth/verify"
        ]
        
        try:
            async with httpx.AsyncClient() as client:
                for endpoint in endpoints_to_test:
                    response = await client.get(f"{self.backend_url}{endpoint}", timeout=5.0)
                    
                    # We expect 401 for protected endpoints without token
                    if response.status_code in [401, 422]:  # 422 for missing request body on POST
                        print(f"✅ Endpoint {endpoint} exists and requires authentication")
                    else:
                        print(f"⚠️  Endpoint {endpoint} returned unexpected status {response.status_code}")
                
                return True
                
        except httpx.RequestError as e:
            print(f"❌ Error testing auth endpoints: {e}")
            return False

    async def test_protected_endpoints(self):
        """Test that protected endpoints require authentication"""
        print("🔧 Testing protected endpoints...")
        
        protected_endpoints = [
            "/api/profiles/",
            "/api/scraping/",
            "/api/messaging/"
        ]
        
        try:
            async with httpx.AsyncClient() as client:
                for endpoint in protected_endpoints:
                    response = await client.get(f"{self.backend_url}{endpoint}", timeout=5.0)
                    
                    if response.status_code == 401:
                        print(f"✅ Endpoint {endpoint} properly requires authentication")
                    else:
                        print(f"⚠️  Endpoint {endpoint} returned status {response.status_code} (expected 401)")
                
                return True
                
        except httpx.RequestError as e:
            print(f"❌ Error testing protected endpoints: {e}")
            return False

    async def test_google_oauth_config(self):
        """Test Google OAuth configuration"""
        print("🔧 Testing Google OAuth configuration...")
        
        try:
            async with httpx.AsyncClient() as client:
                # Test if Google OAuth route exists
                response = await client.get(
                    f"{self.auto_login_url}/auth/google",
                    follow_redirects=False,
                    timeout=5.0
                )
                
                if response.status_code in [302, 401, 403]:
                    print("✅ Google OAuth endpoint exists")
                    return True
                else:
                    print(f"⚠️  Google OAuth endpoint returned status {response.status_code}")
                    return False
                    
        except httpx.RequestError as e:
            print(f"❌ Error testing Google OAuth: {e}")
            return False

    async def test_package_validation(self):
        """Test package validation endpoints"""
        print("🔧 Testing package validation...")
        
        try:
            async with httpx.AsyncClient() as client:
                # Test package access endpoint (should require auth)
                response = await client.get(
                    f"{self.auto_login_url}/users/1/package-access",
                    timeout=5.0
                )
                
                if response.status_code == 401:
                    print("✅ Package access endpoint requires authentication")
                    return True
                else:
                    print(f"⚠️  Package access endpoint returned status {response.status_code}")
                    return False
                    
        except httpx.RequestError as e:
            print(f"❌ Error testing package validation: {e}")
            return False

    async def run_all_tests(self):
        """Run all integration tests"""
        print("🚀 Starting Authentication Integration Tests\n")
        
        tests = [
            ("Auto-login Service", self.test_auto_login_service),
            ("Backend Service", self.test_backend_service),
            ("Auth Endpoints", self.test_auth_endpoints),
            ("Protected Endpoints", self.test_protected_endpoints),
            ("Google OAuth Config", self.test_google_oauth_config),
            ("Package Validation", self.test_package_validation),
        ]
        
        results = []
        
        for test_name, test_func in tests:
            print(f"\n📋 Running {test_name} test...")
            try:
                result = await test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name} test failed with exception: {e}")
                results.append((test_name, False))
        
        # Print summary
        print("\n" + "="*50)
        print("📊 TEST SUMMARY")
        print("="*50)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} - {test_name}")
            if result:
                passed += 1
        
        print(f"\nResults: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Authentication system is ready.")
        else:
            print("⚠️  Some tests failed. Please check the configuration.")
            
        return passed == total

async def main():
    """Main test function"""
    tester = AuthIntegrationTester()
    success = await tester.run_all_tests()
    
    if not success:
        print("\n🔧 TROUBLESHOOTING TIPS:")
        print("1. Make sure auto-login service is running: npm run dev (in auto-login directory)")
        print("2. Make sure backend service is running: python main.py (in backend directory)")
        print("3. Check environment variables:")
        print(f"   - AUTO_LOGIN_SERVICE_URL={AUTO_LOGIN_SERVICE_URL}")
        print(f"   - BACKEND_API_URL={BACKEND_API_URL}")
        print("4. Verify Google OAuth credentials are configured in auto-login service")
        print("5. Check database connection and migrations")
        
        sys.exit(1)
    
    print("\n✅ Integration tests completed successfully!")

if __name__ == "__main__":
    asyncio.run(main())
