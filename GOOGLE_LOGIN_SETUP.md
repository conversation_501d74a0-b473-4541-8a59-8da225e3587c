# Google Login Setup Guide

Hướng dẫn thiết lập chức năng login bằng Google cho hệ thống Facebook Automation.

## 🏗️ Kiến trúc hệ thống

```
Frontend (React) ←→ Auto-login Service (NestJS) ←→ Backend API (FastAPI)
     ↓                        ↓                           ↓
  Google OAuth           JWT Authentication         Protected Routes
```

## 📋 Yêu cầu

1. **Auto-login Service (NestJS)**: Xử lý Google OAuth và JWT
2. **Backend API (FastAPI)**: Cung cấp các API được bảo vệ
3. **Frontend (React)**: Giao diện người dùng với Google login
4. **Database**: PostgreSQL với bảng users và packages

## 🔧 Cài đặt

### 1. Cấu hình Google OAuth

1. Truy cập [Google Cloud Console](https://console.cloud.google.com/)
2. Tạo project mới hoặc chọn project hiện có
3. Bật Google+ API
4. Tạo OAuth 2.0 credentials:
   - Application type: Web application
   - Authorized redirect URIs: `http://localhost:3000/auth/google/callback`

### 2. <PERSON><PERSON>u hình Auto-login Service

Tạo file `.env` trong thư mục `auto-login/`:

```env
# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=your_username
DATABASE_PASSWORD=your_password
DATABASE_NAME=your_database

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=1h

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:3000/auth/google/callback

# CORS
FRONTEND_URL=http://localhost:3001
```

### 3. Cấu hình Backend API

Tạo file `.env` trong thư mục `backend/`:

```env
# Auto-login service URL
AUTO_LOGIN_SERVICE_URL=http://localhost:3000

# API settings
API_HOST=0.0.0.0
API_PORT=8000
```

### 4. Cấu hình Frontend

Tạo file `.env` trong thư mục `frontend/`:

```env
REACT_APP_AUTO_LOGIN_SERVICE_URL=http://localhost:3000
REACT_APP_BACKEND_API_URL=http://localhost:8000
```

## 🚀 Khởi chạy hệ thống

### 1. Khởi động Auto-login Service

```bash
cd auto-login
npm install
npm run migration:run
npm run dev
```

### 2. Khởi động Backend API

```bash
cd backend
pip install -r requirements.txt
python main.py
```

### 3. Khởi động Frontend

```bash
cd frontend
npm install
npm start
```

## 🔐 Luồng xác thực

### 1. Google OAuth Flow

1. User click "Sign in with Google" trên frontend
2. Frontend mở popup window đến `http://localhost:3000/auth/google`
3. Auto-login service redirect đến Google OAuth
4. User đăng nhập Google và authorize
5. Google redirect về `http://localhost:3000/auth/google/callback`
6. Auto-login service:
   - Xác thực user với Google
   - Kiểm tra user trong database
   - Kiểm tra user status = 'active'
   - Kiểm tra user có package với `has_bot_insta = true`
   - Tạo JWT token
   - Set HTTP-only cookies
   - Gửi message về frontend

### 2. API Authentication

1. Frontend gửi request đến Backend API
2. Backend API gọi Auto-login service để verify JWT token
3. Auto-login service trả về user info
4. Backend API kiểm tra package access
5. Nếu hợp lệ, cho phép truy cập API

## 🛡️ Bảo mật

### JWT Token
- Access token: 1 giờ (HTTP-only cookie)
- Refresh token: 7 ngày (HTTP-only cookie)
- Secure cookies trong production

### Package Validation
- User phải có status = 'active'
- User phải có package với `has_bot_insta = true`
- Package phải chưa hết hạn (`expires_at > now()`)

### API Protection
- Tất cả API endpoints yêu cầu authentication
- Middleware kiểm tra JWT token
- Middleware kiểm tra package access

## 🧪 Testing

Chạy integration test:

```bash
cd backend
python test_auth_integration.py
```

Test sẽ kiểm tra:
- ✅ Auto-login service connection
- ✅ Backend API connection  
- ✅ Authentication endpoints
- ✅ Protected endpoints
- ✅ Google OAuth configuration
- ✅ Package validation

## 📊 Database Schema

### Users Table
```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  email VARCHAR UNIQUE NOT NULL,
  password VARCHAR,
  plain_password VARCHAR,
  status VARCHAR DEFAULT 'inactive',
  discord_id VARCHAR UNIQUE,
  google_id VARCHAR UNIQUE,
  role VARCHAR DEFAULT 'user',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Packages Table
```sql
CREATE TABLE packages (
  id SERIAL PRIMARY KEY,
  name VARCHAR NOT NULL,
  description TEXT,
  has_bot_insta BOOLEAN DEFAULT FALSE,
  -- other fields...
);
```

### Package Users Table
```sql
CREATE TABLE package_users (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id),
  package_id INTEGER REFERENCES packages(id),
  start_date TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP NOT NULL,
  status VARCHAR DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## 🔍 Troubleshooting

### Common Issues

1. **Google OAuth Error**
   - Kiểm tra Google Client ID/Secret
   - Kiểm tra redirect URI trong Google Console
   - Kiểm tra CORS settings

2. **JWT Token Invalid**
   - Kiểm tra JWT_SECRET trong .env
   - Kiểm tra token expiration
   - Clear browser cookies

3. **Package Access Denied**
   - Kiểm tra user status trong database
   - Kiểm tra package `has_bot_insta = true`
   - Kiểm tra package expiration date

4. **API Connection Error**
   - Kiểm tra service URLs trong .env
   - Kiểm tra services đang chạy
   - Kiểm tra firewall/network

### Debug Commands

```bash
# Check auto-login service
curl http://localhost:3000/health

# Check backend API
curl http://localhost:8000/health

# Check protected endpoint (should return 401)
curl http://localhost:8000/api/profiles/

# Check user package access (with valid token)
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:3000/users/1/package-access
```

## 📝 API Endpoints

### Auto-login Service
- `GET /auth/google` - Start Google OAuth
- `GET /auth/google/callback` - OAuth callback
- `GET /auth/verify` - Verify JWT token
- `GET /users/:id/package-access` - Check package access

### Backend API
- `POST /api/auth/login` - Login with token
- `GET /api/auth/me` - Get current user
- `GET /api/auth/verify` - Verify token
- `GET /api/profiles/` - Protected: Get profiles
- `GET /api/scraping/` - Protected: Get scraping tasks
- `GET /api/messaging/` - Protected: Get messaging tasks

## ✅ Checklist

- [ ] Google OAuth credentials configured
- [ ] Database tables created
- [ ] Environment variables set
- [ ] All services running
- [ ] Integration tests passing
- [ ] Frontend login working
- [ ] API endpoints protected
- [ ] Package validation working
