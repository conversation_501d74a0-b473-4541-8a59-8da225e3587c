import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-google-oauth20';
import { ConfigService } from '@nestjs/config';
import { AuthService } from './auth.service';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor(
    private configService: ConfigService,
    private authService: AuthService,
  ) {
    super({
      clientID: configService.get('google.clientId'),
      clientSecret: configService.get('google.clientSecret'),
      callbackURL: configService.get('google.callbackURL'),
      scope: ['email', 'profile'],
    });
  }

  async validate(_accessToken: string, _refreshToken: string, profile: any) {
    console.log('🔍 [GOOGLE_STRATEGY] Validating Google profile:', profile);

    const { id: googleId, emails } = profile;
    const email = emails && emails.length > 0 ? emails[0].value : null;

    console.log('🔍 [GOOGLE_STRATEGY] Extracted data - ID:', googleId, 'Email:', email);

    // Return the user data that will be passed to the controller
    const userData = { id: googleId, email };
    console.log('✅ [GOOGLE_STRATEGY] Returning user data:', userData);

    return userData;
  }
}
