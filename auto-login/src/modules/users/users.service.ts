import {
  Injectable,
  NotFoundException,
  ConflictException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { CreateTrialUserDto } from './dto/create-trial-user.dto';
import * as bcrypt from 'bcrypt';
import { PackageUser } from '../packages/entities/package-user.entity';
import { Package } from '../packages/entities/package.entity';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly usersRepository: Repository<User>,
    @InjectRepository(PackageUser)
    private readonly packageUserRepository: Repository<PackageUser>,
    @InjectRepository(Package)
    private readonly packageRepository: Repository<Package>,
  ) {}

  async findById(id: number): Promise<User> {
    const user = await this.usersRepository.findOne({ where: { id } });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return user;
  }

  async updateStatus(id: number, status: string): Promise<Partial<User>> {
    const user = await this.usersRepository.findOne({ where: { id } });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    user.status = status;
    const savedUser = await this.usersRepository.save(user);

    // Return only safe fields
    return {
      id: savedUser.id,
      email: savedUser.email,
      status: savedUser.status,
      role: savedUser.role,
      created_at: savedUser.created_at,
      updated_at: savedUser.updated_at,
    };
  }

  async createTrialUser(
    createTrialUserDto: CreateTrialUserDto,
  ): Promise<Partial<User> & { plain_password: string }> {
    // Check if user with this email already exists
    const existingUser = await this.usersRepository.findOne({
      where: { email: createTrialUserDto.email },
    });

    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    // Use provided password or generate a random one
    const password =
      createTrialUserDto.password || Math.random().toString(36).slice(-8);
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create the trial user
    const user = this.usersRepository.create({
      email: createTrialUserDto.email,
      password: hashedPassword,
      plain_password: password, // Store the plain password
      status: 'trial',
      role: 'user',
    });

    const savedUser = await this.usersRepository.save(user);

    // Assign user to the selected package
    const packageUser = this.packageUserRepository.create({
      user_id: savedUser.id,
      package_id: createTrialUserDto.package_id,
      start_date: new Date(),
      expires_at: new Date(
        Date.now() + createTrialUserDto.trial_days * 24 * 60 * 60 * 1000,
      ),
      status: 'active',
    });

    await this.packageUserRepository.save(packageUser);

    // Return only safe fields plus plain_password for admin use
    return {
      id: savedUser.id,
      email: savedUser.email,
      status: savedUser.status,
      role: savedUser.role,
      created_at: savedUser.created_at,
      updated_at: savedUser.updated_at,
      plain_password: password, // Include plain password for admin
    };
  }

  async findAllTrialUsers(page: number = 1, limit: number = 10) {
    const skip = (page - 1) * limit;

    const [users, total] = await this.usersRepository.findAndCount({
      where: { status: 'trial' },
      relations: ['packageUsers'],
      select: [
        'id',
        'email',
        'status',
        'role',
        'created_at',
        'updated_at',
        // Exclude password, plain_password, discord_id, google_id
      ],
      order: { created_at: 'DESC' },
      skip,
      take: limit,
    });

    return {
      data: users,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async removeUser(id: number): Promise<void> {
    const user = await this.usersRepository.findOne({ where: { id } });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    await this.usersRepository.remove(user);
  }

  async findTrialUserById(id: number) {
    const user = await this.usersRepository.findOne({
      where: { id, status: 'trial' },
      relations: ['packageUsers', 'packageUsers.package'],
      select: [
        'id',
        'email',
        'status',
        'role',
        'created_at',
        'updated_at',
        // Exclude password, plain_password, discord_id, google_id
      ],
    });

    if (!user) {
      throw new NotFoundException(`Trial user with ID ${id} not found`);
    }

    // Rename package property to packageInfo to avoid EJS reserved keyword issues
    if (user.packageUsers && user.packageUsers.length > 0) {
      user.packageUsers.forEach((pu: any) => {
        if (pu.package) {
          pu.packageInfo = pu.package;
          delete pu.package;
        }
      });
    }

    return user;
  }

  async validateGoogleLogin(
    googleId: string,
    email: string,
  ): Promise<{
    user: Partial<User>;
    hasValidPackage: boolean;
    message?: string;
  }> {
    // Find user by google_id or email
    let user = await this.usersRepository.findOne({
      where: { google_id: googleId },
      relations: ['packageUsers', 'packageUsers.package'],
    });

    if (!user) {
      // Try to find by email
      user = await this.usersRepository.findOne({
        where: { email },
        relations: ['packageUsers', 'packageUsers.package'],
      });

      if (user) {
        // Update user with google_id if found by email
        user.google_id = googleId;
        await this.usersRepository.save(user);
      }
    }

    if (!user) {
      throw new NotFoundException(
        'User not found. Please contact administrator to create your account.',
      );
    }

    // Check if user is active
    if (user.status !== 'active') {
      throw new UnauthorizedException(
        `Account is ${user.status}. Please contact administrator.`,
      );
    }

    // Check if user has active package with has_bot_insta = true
    const hasValidPackage = await this.checkUserHasBotInstaPackage(user.id);

    return {
      user: {
        id: user.id,
        email: user.email,
        status: user.status,
        role: user.role,
        created_at: user.created_at,
        updated_at: user.updated_at,
      },
      hasValidPackage,
      message: hasValidPackage
        ? 'Login successful'
        : 'Account found but no active package with bot Instagram feature. Please purchase a valid package.',
    };
  }

  async checkUserHasBotInstaPackage(userId: number): Promise<boolean> {
    // Get all active package_users for the user
    const packageUsers = await this.packageUserRepository.find({
      where: {
        user_id: userId,
        status: 'active',
      },
      relations: ['package'],
    });

    if (!packageUsers || packageUsers.length === 0) {
      return false;
    }

    // Check if any of the user's active packages has has_bot_insta = true
    return packageUsers.some(
      (packageUser) =>
        packageUser.package &&
        packageUser.package.has_bot_insta === true &&
        packageUser.expires_at > new Date(),
    );
  }

  async findByGoogleId(googleId: string): Promise<User | null> {
    return await this.usersRepository.findOne({
      where: { google_id: googleId },
      relations: ['packageUsers', 'packageUsers.package'],
    });
  }

  async findByEmail(email: string): Promise<User | null> {
    return await this.usersRepository.findOne({
      where: { email },
      relations: ['packageUsers', 'packageUsers.package'],
    });
  }
}
