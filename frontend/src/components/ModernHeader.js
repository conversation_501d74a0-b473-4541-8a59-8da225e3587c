/**
 * Modern Header Component - Desktop Design
 */

import React, { useState, useEffect } from 'react';
import { Layout, Button, Space, Badge, Dropdown, Avatar, Input, Tooltip, notification } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BellOutlined,
  SearchOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  InfoCircleOutlined,
  MinusOutlined,
  BorderOutlined,
  CloseOutlined,
  WifiOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';

// Import contexts
import { useAuth } from '../contexts/AuthContext';

const { Header } = Layout;
const { Search } = Input;

const ModernHeader = ({ collapsed, setCollapsed }) => {
  const { user, logout, hasValidPackage } = useAuth();
  const [notifications, setNotifications] = useState([
    { id: 1, title: 'System Update', message: 'New version available', time: '2 min ago', type: 'info' },
    { id: 2, title: 'Task Completed', message: 'Scraping task finished successfully', time: '5 min ago', type: 'success' },
    { id: 3, title: 'Warning', message: 'High memory usage detected', time: '10 min ago', type: 'warning' }
  ]);
  const [connectionStatus, setConnectionStatus] = useState('connected');
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    // Update time every second
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timeInterval);
  }, []);

  const handleLogout = async () => {
    try {
      await logout();
      notification.success({
        message: 'Logged Out',
        description: 'You have been successfully logged out.',
        duration: 3,
      });
    } catch (error) {
      notification.error({
        message: 'Logout Error',
        description: 'Failed to logout properly.',
        duration: 3,
      });
    }
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Profile Settings',
    },
    {
      key: 'about',
      icon: <InfoCircleOutlined />,
      label: 'About Application',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Sign Out',
      onClick: handleLogout
    },
  ];

  const notificationMenuItems = notifications.map(notif => ({
    key: notif.id,
    label: (
      <div style={{ width: '300px', padding: '8px 0' }}>
        <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{notif.title}</div>
        <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>{notif.message}</div>
        <div style={{ fontSize: '11px', color: '#999' }}>{notif.time}</div>
      </div>
    ),
  }));

  const handleWindowControl = (action) => {
    if (window.electronAPI) {
      switch (action) {
        case 'minimize':
          window.electronAPI.minimizeWindow();
          break;
        case 'maximize':
          window.electronAPI.maximizeWindow();
          break;
        case 'close':
          window.electronAPI.closeWindow();
          break;
      }
    }
  };

  const handleSearch = (value) => {
    if (value) {
      notification.info({
        message: 'Search',
        description: `Searching for: ${value}`,
        duration: 2
      });
    }
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return '#52c41a';
      case 'connecting': return '#1890ff';
      case 'disconnected': return '#ff4d4f';
      default: return '#d9d9d9';
    }
  };

  return (
    <Header 
      className="modern-header"
      style={{
        position: 'fixed',
        top: 0,
        zIndex: 999,
        width: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(20px)',
        boxShadow: '0 2px 20px rgba(0,0,0,0.1)',
        padding: '0 24px',
        marginLeft: collapsed ? 80 : 280,
        width: collapsed ? 'calc(100% - 80px)' : 'calc(100% - 280px)',
        transition: 'all 0.3s ease',
        border: 'none',
        borderBottom: '1px solid rgba(0,0,0,0.06)'
      }}
    >
      {/* Left Section */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
        {/* Menu Toggle */}
        <Button
          type="text"
          icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={() => setCollapsed(!collapsed)}
          style={{
            fontSize: '18px',
            width: '40px',
            height: '40px',
            borderRadius: '12px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)'
          }}
        />

        {/* App Title */}
        <div>
          <h1 style={{ 
            margin: 0, 
            fontSize: '24px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: 'bold'
          }}>
            Facebook Automation Desktop
          </h1>
          <div style={{ 
            fontSize: '12px', 
            color: '#666',
            marginTop: '-2px'
          }}>
            {currentTime.toLocaleString()}
          </div>
        </div>
      </div>

      {/* Center Section - Search */}
      <div style={{ flex: 1, maxWidth: '400px', margin: '0 24px' }}>
        <Search
          placeholder="Search profiles, tasks, or settings..."
          allowClear
          enterButton={<SearchOutlined />}
          size="large"
          onSearch={handleSearch}
          style={{
            borderRadius: '12px'
          }}
        />
      </div>

      {/* Right Section */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        {/* Connection Status */}
        <Tooltip title={`Connection: ${connectionStatus}`}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            padding: '6px 12px',
            background: 'rgba(0,0,0,0.04)',
            borderRadius: '20px',
            fontSize: '12px'
          }}>
            <WifiOutlined style={{ color: getConnectionStatusColor() }} />
            <span style={{ color: '#666', textTransform: 'capitalize' }}>
              {connectionStatus}
            </span>
          </div>
        </Tooltip>

        {/* Performance Indicator */}
        <Tooltip title="System Performance">
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            padding: '6px 12px',
            background: 'rgba(0,0,0,0.04)',
            borderRadius: '20px',
            fontSize: '12px'
          }}>
            <ThunderboltOutlined style={{ color: '#52c41a' }} />
            <span style={{ color: '#666' }}>Optimal</span>
          </div>
        </Tooltip>

        {/* Notifications */}
        <Dropdown
          menu={{ items: notificationMenuItems }}
          placement="bottomRight"
          arrow
          trigger={['click']}
        >
          <Button
            type="text"
            style={{
              width: '40px',
              height: '40px',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <Badge count={notifications.length} size="small">
              <BellOutlined style={{ fontSize: '18px', color: '#666' }} />
            </Badge>
          </Button>
        </Dropdown>

        {/* Settings */}
        <Tooltip title="Settings">
          <Button
            type="text"
            icon={<SettingOutlined />}
            onClick={() => window.location.hash = '/settings'}
            style={{
              fontSize: '18px',
              width: '40px',
              height: '40px',
              borderRadius: '12px',
              color: '#666',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          />
        </Tooltip>

        {/* User Profile */}
        <Dropdown
          menu={{ items: userMenuItems }}
          placement="bottomRight"
          arrow
          trigger={['click']}
        >
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            cursor: 'pointer',
            padding: '4px 12px',
            borderRadius: '12px',
            background: 'rgba(0,0,0,0.04)',
            transition: 'background 0.3s'
          }}>
            <Avatar 
              style={{ 
                backgroundColor: '#667eea',
                marginRight: '8px'
              }}
              icon={<UserOutlined />}
            />
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#333' }}>
                {user?.email?.split('@')[0] || 'User'}
              </span>
              <span style={{ fontSize: '12px', color: '#666' }}>
                {user?.role || 'User'} {hasValidPackage ? '• Bot Access' : '• Limited Access'}
              </span>
            </div>
          </div>
        </Dropdown>

        {/* Window Controls (for Electron) */}
        <div style={{ display: 'flex', gap: '4px', marginLeft: '12px' }}>
          <Button
            type="text"
            icon={<MinusOutlined />}
            onClick={() => handleWindowControl('minimize')}
            style={{
              width: '32px',
              height: '32px',
              borderRadius: '8px',
              fontSize: '12px',
              color: '#666',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          />
          <Button
            type="text"
            icon={<BorderOutlined />}
            onClick={() => handleWindowControl('maximize')}
            style={{
              width: '32px',
              height: '32px',
              borderRadius: '8px',
              fontSize: '12px',
              color: '#666',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          />
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={() => handleWindowControl('close')}
            style={{
              width: '32px',
              height: '32px',
              borderRadius: '8px',
              fontSize: '12px',
              color: '#ff4d4f',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          />
        </div>
      </div>

      {/* Custom Styles */}
      <style jsx>{`
        .modern-header .ant-input-search .ant-input-group .ant-input-affix-wrapper {
          border-radius: 12px 0 0 12px;
          border-right: none;
        }

        .modern-header .ant-input-search .ant-input-group .ant-btn {
          border-radius: 0 12px 12px 0;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-color: #667eea;
        }

        .modern-header .ant-input-search .ant-input-group .ant-btn:hover {
          background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
          border-color: #5a6fd8;
        }

        .modern-header .ant-btn:hover {
          background: rgba(0,0,0,0.06) !important;
        }
      `}</style>
    </Header>
  );
};

export default ModernHeader;
