/**
 * Protected Route Component - Handles authentication and package access checks
 */

import React from 'react';
import { Card, Typography, Space, Alert, Button, Spin } from 'antd';
import { ExclamationCircleOutlined, LockOutlined, ShoppingCartOutlined } from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';
import Login from './Login';

const { Title, Text } = Typography;

const ProtectedRoute = ({ children, requirePackageAccess = true }) => {
  const { isAuthenticated, loading, user, hasValidPackage, logout } = useAuth();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <Card style={{ textAlign: 'center', borderRadius: 16 }}>
          <Space direction="vertical" size="large">
            <Spin size="large" />
            <Text>Checking authentication...</Text>
          </Space>
        </Card>
      </div>
    );
  }

  // Show login if not authenticated
  if (!isAuthenticated) {
    return <Login />;
  }

  // Show package access required message if user doesn't have valid package
  if (requirePackageAccess && !hasValidPackage) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <Card 
          style={{ 
            width: 500, 
            textAlign: 'center',
            borderRadius: 16,
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
          }}
        >
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <ShoppingCartOutlined style={{ fontSize: 48, color: '#faad14' }} />
            
            <Title level={3} style={{ margin: 0, color: '#faad14' }}>
              Package Access Required
            </Title>
            
            <Alert
              message="Access Denied"
              description="You need an active package with bot Instagram feature to access this application."
              type="warning"
              icon={<ExclamationCircleOutlined />}
              showIcon
              style={{ textAlign: 'left' }}
            />

            <div style={{ textAlign: 'left' }}>
              <Text strong>Current Account:</Text>
              <br />
              <Text type="secondary">{user?.email}</Text>
              <br />
              <Text type="secondary">Status: {user?.status}</Text>
              <br />
              <Text type="secondary">Role: {user?.role}</Text>
            </div>

            <div>
              <Text type="secondary">
                Please contact your administrator to purchase a valid package with bot Instagram feature.
              </Text>
            </div>

            <Space>
              <Button 
                type="default" 
                onClick={logout}
                icon={<LockOutlined />}
              >
                Sign Out
              </Button>
              <Button 
                type="primary"
                onClick={() => window.location.reload()}
              >
                Refresh Status
              </Button>
            </Space>
          </Space>
        </Card>
      </div>
    );
  }

  // Show account inactive message if user account is not active
  if (user?.status !== 'active') {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <Card 
          style={{ 
            width: 500, 
            textAlign: 'center',
            borderRadius: 16,
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
          }}
        >
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <ExclamationCircleOutlined style={{ fontSize: 48, color: '#ff4d4f' }} />
            
            <Title level={3} style={{ margin: 0, color: '#ff4d4f' }}>
              Account {user?.status}
            </Title>
            
            <Alert
              message="Account Access Restricted"
              description={`Your account is currently ${user?.status}. Please contact administrator to activate your account.`}
              type="error"
              showIcon
              style={{ textAlign: 'left' }}
            />

            <div style={{ textAlign: 'left' }}>
              <Text strong>Account Details:</Text>
              <br />
              <Text type="secondary">{user?.email}</Text>
              <br />
              <Text type="secondary">Status: {user?.status}</Text>
              <br />
              <Text type="secondary">Role: {user?.role}</Text>
            </div>

            <Button 
              type="primary" 
              onClick={logout}
              icon={<LockOutlined />}
            >
              Sign Out
            </Button>
          </Space>
        </Card>
      </div>
    );
  }

  // User is authenticated and has valid access, render children
  return children;
};

export default ProtectedRoute;
