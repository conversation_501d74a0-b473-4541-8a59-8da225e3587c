/**
 * Authentication Context - Manages user authentication state
 */

import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

// Auto-login service URL
const AUTO_LOGIN_SERVICE_URL = 'http://localhost:3002';

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [hasValidPackage, setHasValidPackage] = useState(false);

  // Check authentication status on app load
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      setLoading(true);
      
      // Check if user data exists in localStorage
      const storedUser = localStorage.getItem('user');
      const storedAuth = localStorage.getItem('isAuthenticated');
      
      if (storedUser && storedAuth === 'true') {
        // Verify with server
        const response = await fetch(`${AUTO_LOGIN_SERVICE_URL}/auth/verify`, {
          method: 'GET',
          credentials: 'include', // Include HTTP-only cookies
        });

        if (response.ok) {
          const data = await response.json();
          setUser(data.user);
          setIsAuthenticated(true);
          
          // Check package access
          await checkPackageAccess(data.user.id);
        } else {
          // Token is invalid, clear local storage
          clearAuthData();
        }
      } else {
        clearAuthData();
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      clearAuthData();
    } finally {
      setLoading(false);
    }
  };

  const checkPackageAccess = async (userId) => {
    try {
      const response = await fetch(`${AUTO_LOGIN_SERVICE_URL}/users/${userId}/package-access`, {
        method: 'GET',
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setHasValidPackage(data.has_bot_insta_access);
      } else {
        setHasValidPackage(false);
      }
    } catch (error) {
      console.error('Package access check failed:', error);
      setHasValidPackage(false);
    }
  };

  const login = async (userData) => {
    setUser(userData);
    setIsAuthenticated(true);
    localStorage.setItem('user', JSON.stringify(userData));
    localStorage.setItem('isAuthenticated', 'true');
    
    // Check package access after login
    await checkPackageAccess(userData.id);
  };

  const logout = async () => {
    try {
      // Call logout endpoint to clear server-side session
      await fetch(`${AUTO_LOGIN_SERVICE_URL}/auth/logout`, {
        method: 'POST',
        credentials: 'include',
      });
    } catch (error) {
      console.error('Logout request failed:', error);
    } finally {
      clearAuthData();
    }
  };

  const clearAuthData = () => {
    setUser(null);
    setIsAuthenticated(false);
    setHasValidPackage(false);
    localStorage.removeItem('user');
    localStorage.removeItem('isAuthenticated');
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    hasValidPackage,
    login,
    logout,
    checkAuthStatus,
    checkPackageAccess: () => user ? checkPackageAccess(user.id) : Promise.resolve(),
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
