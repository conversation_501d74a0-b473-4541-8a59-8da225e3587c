#!/bin/bash

# Facebook Automation Desktop - Web Development Script
# This script runs the application in web browser for easier debugging

set -e

echo "🌐 Starting Facebook Automation Desktop (Web Development Mode)..."

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Change to project root
cd "$PROJECT_ROOT"

echo "📁 Project root: $PROJECT_ROOT"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# PIDs for cleanup
BACKEND_PID=""

# Cleanup function
cleanup() {
    print_info "Shutting down services..."
    
    if [ -n "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        print_info "Backend stopped"
    fi
    
    # Kill any remaining processes
    pkill -f "uvicorn main:app" 2>/dev/null || true
    pkill -f "webpack serve" 2>/dev/null || true
    
    print_success "All services stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Check if backend is already running
print_info "Checking if backend is running..."
if curl -s http://127.0.0.1:8000/health > /dev/null 2>&1; then
    print_success "Backend is already running"
else
    print_info "Starting backend..."
    cd backend
    
    # Activate virtual environment if it exists
    if [ -d "venv" ]; then
        print_info "Activating virtual environment..."
        source venv/bin/activate
    fi
    
    # Start backend in background
    python -m uvicorn main:app --reload --host 127.0.0.1 --port 8000 &
    BACKEND_PID=$!
    
    # Wait for backend to start
    print_info "Waiting for backend to start..."
    for i in {1..30}; do
        if curl -s http://127.0.0.1:8000/health > /dev/null 2>&1; then
            print_success "Backend started successfully"
            break
        fi
        sleep 1
    done
    
    cd "$PROJECT_ROOT"
fi

# Start frontend web development server
print_info "Starting frontend web development server..."
cd frontend

print_success "🚀 Starting services..."
print_info "Backend API: http://localhost:8000"
print_info "API Docs: http://localhost:8000/docs"
print_info "Frontend: http://localhost:3002"
print_warning "Press Ctrl+C to stop all services"

# Start webpack dev server
npm run serve:web

# This will run until interrupted
wait
